import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/app_colors.dart';
// Dimens import 제거됨 (사용하지 않음)
import '../../providers/unified_workspace_provider.dart';
import '../../providers/nickname_provider.dart';

/// 스플래시 스크린
/// 앱 시작 시 로딩 화면을 표시하고 필요한 데이터를 미리 로딩합니다.
class SplashScreen extends ConsumerStatefulWidget {
  final VoidCallback? onInitializationComplete;

  const SplashScreen({
    super.key,
    this.onInitializationComplete,
  });

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _fadeController;
  late Animation<double> _logoAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // 애니메이션 컨트롤러 초기화
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 애니메이션 설정
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // 초기화 시작
    _initializeApp();
  }

  @override
  void dispose() {
    _logoController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  /// 앱 초기화 및 데이터 로딩
  Future<void> _initializeApp() async {
    try {
      // 로고 애니메이션 시작
      _logoController.forward();
      
      // 약간의 지연 후 페이드 애니메이션 시작
      await Future.delayed(const Duration(milliseconds: 500));
      _fadeController.forward();

      // 필요한 데이터 로딩 (병렬 처리)
      await Future.wait([
        _loadWorkspaces(),
        _preloadData(),
        // 최소 스플래시 표시 시간 보장 (닉네임 로딩 완료 대기)
        Future.delayed(const Duration(milliseconds: 4000)),
      ]);

      // 초기화 완료 콜백 호출
      if (mounted && widget.onInitializationComplete != null) {
        widget.onInitializationComplete!();
      }
    } catch (e) {
      // 오류 발생 시에도 콜백 호출
      if (mounted && widget.onInitializationComplete != null) {
        widget.onInitializationComplete!();
      }
    }
  }

  /// 워크스페이스 로딩
  Future<void> _loadWorkspaces() async {
    try {
      await ref.read(unifiedWorkspaceProvider.notifier).refresh();
    } catch (e) {
      // 오류 무시하고 계속 진행
    }
  }

  /// 기본 데이터 미리 로딩 (최적화: 필수 데이터만)
  Future<void> _preloadData() async {
    try {
      // 닉네임 로딩 (필수 - 닉네임 등록창 방지)
      await ref.read(nicknameProvider.notifier).loadNickname();

      // 다른 데이터는 각 화면에서 필요할 때 로드하도록 변경
      // 이렇게 하면 앱 시작 속도가 빨라지고 중복 로딩이 방지됩니다
    } catch (e) {
      // 오류 무시하고 계속 진행
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width >= 600;

    return Scaffold(
      backgroundColor: AppColors.primarySeed,
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primarySeed,
                AppColors.primarySeed.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 상단 여백
              const Spacer(flex: 2),
              
              // 로고와 앱 이름 통합 영역
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 로고 아이콘
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: MediaQuery.of(context).orientation == Orientation.landscape
                                ? MediaQuery.of(context).size.height * 0.2
                                : (isTablet ? 120 : 100).toDouble(),
                            height: MediaQuery.of(context).orientation == Orientation.landscape
                                ? MediaQuery.of(context).size.height * 0.2
                                : (isTablet ? 120 : 100).toDouble(),
                            constraints: BoxConstraints(
                              minWidth: 80,
                              maxWidth: 160,
                              minHeight: 80,
                              maxHeight: 160,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                MediaQuery.of(context).orientation == Orientation.landscape
                                    ? MediaQuery.of(context).size.height * 0.04
                                    : (isTablet ? 24 : 20).toDouble(),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                'B',
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: isTablet ? 48 : 40,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primarySeed,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    
                    // 아이콘과 이름 사이 간격 (더 가깝게)
                    const SizedBox(height: 16),
                    
                    // 앱 이름
                    AnimatedBuilder(
                      animation: _fadeAnimation,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _fadeAnimation.value,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              FittedBox(
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxWidth: MediaQuery.of(context).orientation == Orientation.landscape
                                        ? MediaQuery.of(context).size.width * 0.3
                                        : double.infinity,
                                  ),
                                  child: Text(
                                    '바라 부스 매니저',
                                    style: TextStyle(
                                      fontFamily: 'Pretendard',
                                      fontSize: MediaQuery.of(context).orientation == Orientation.landscape
                                          ? (isTablet ? 24 : 20)
                                          : (isTablet ? 32 : 28),
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      letterSpacing: 1.2,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              FittedBox(
                                child: Container(
                                  constraints: BoxConstraints(
                                    maxWidth: MediaQuery.of(context).orientation == Orientation.landscape
                                        ? MediaQuery.of(context).size.width * 0.3
                                        : double.infinity,
                                  ),
                                  child: Text(
                                    '동인 부스 전문 매니저 앱',
                                    style: TextStyle(
                                      fontFamily: 'Pretendard',
                                      fontSize: MediaQuery.of(context).orientation == Orientation.landscape
                                          ? (isTablet ? 12 : 10)
                                          : (isTablet ? 16 : 14),
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white.withValues(alpha: 0.8),
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              // 하단 여백
              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }
}
